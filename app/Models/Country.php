<?php

namespace App\Models;

use App\Traits\HasTranslations;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class Country extends BaseModel
{
    use Cachable,
        HasTranslations
    ;

    protected $fillable = [
        'code',
        'iso3',
        'systemname',
        'is_deliverable',
        'is_favorite',
    ];

    protected $translatable = [
        'name',
    ];

    protected $casts = [];

}
