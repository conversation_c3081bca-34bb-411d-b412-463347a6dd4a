<?php

declare(strict_types=1);

use App\Models\Country;
use App\Rules\NotExists;
use App\Services\DataMapperService;

describe('Data Mapper Service Test', function () {
    it('can create a new data mapper', function () {
        $x = Country::count();
        dd($x);
        $mappings = [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:countries,id',
                'before' => [
                    \App\Transformers\CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:locales,id',
                'before' => [
                    \App\Transformers\CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => 'nullable|email:rfc,dns,spoof',
            ],
        ];

        $account = [
            "exact_id" => "22552",
            "exact_uniq_id" => "C7E48EB5-7470-4D3D-B291-059905DECF87",
            "uniq_id" => "C7E48EB5-7470-4D3D-B291-059905DECF87",
            "name" => "Autoschade Pijnaker",
            "country_code" => "NL",
            "city" => "AMSTELVEEN",
            "postal_code" => "1187 NR",
            "address" => "Legmeerdijk 25",
            "debitor_number" => "700145",
            "creditor_number" => null,
            "email" => null,
            "phone" => null,
        ];

        $dataMapper = new DataMapperService(
            $mappings,
            $account
        );


        expect($dataMapper->validated())->toMatchArray($account);
    });
});
